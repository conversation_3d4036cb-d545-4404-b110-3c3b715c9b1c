package lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.rici;

import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditActionType;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditRiciPagerAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.audit.AuditType;
import lu.fujitsu.ts.cgdis.portal.core.domain.person.PersonLight;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciAlertGroup;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPager;
import lu.fujitsu.ts.cgdis.portal.core.domain.rici.RiciPersonWithPagerAndAlertGroups;
import lu.fujitsu.ts.cgdis.portal.webapp.aop.audit.aspect.AbstractAuditAspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractRiciPagerAlertGroupAuditAspect<R, M> extends AbstractAuditAspect<AuditRiciPagerAlertGroup, R, M> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbstractRiciPagerAlertGroupAuditAspect.class);

    public AbstractRiciPagerAlertGroupAuditAspect(AuditActionType auditActionType, Class<AuditRiciPagerAlertGroup> auditModelClass) {
        super(AuditType.RICI_PAGER_ALERT_GROUP, auditActionType, auditModelClass);
    }

    protected void mapPersonToAudit(AuditRiciPagerAlertGroup audit, PersonLight person) {
        if (person == null) {
            LOGGER.warn("Person object is null, cannot map to audit.");
            return;
        }
        audit.setPersonTecId(person.getPersonId());
        audit.setPersonName(person.getFirstName() + " " + person.getLastName());
        audit.setPersonCgdisRegistrationNumber(person.getCgdisRegistrationNumber());
    }

    protected void mapAlertGroupToAudit(AuditRiciPagerAlertGroup audit, RiciAlertGroup alertGroup) {
        if (alertGroup == null) {
            LOGGER.warn("AlertGroup object is null, cannot map to audit.");
            return;
        }
        audit.setAlertGroupTecid(alertGroup.getTecid());
        audit.setAlertGroupName(alertGroup.getName());
        audit.setAlertGroupDescription(alertGroup.getDescription());

        if (alertGroup.getRiciRicSchema() != null) {
            audit.setRiciRicSchemaTecid(alertGroup.getRiciRicSchema().getTecid());
            audit.setRiciRicSchemaAlias(alertGroup.getRiciRicSchema().getSchemaAlias());
        }

        if (alertGroup.getRiciRicRange() != null) {
            audit.setRiciRicRangeTecid(alertGroup.getRiciRicRange().getTecid());
            audit.setRiciRicRangeName(alertGroup.getRiciRicRange().getName());
        }
    }

    protected void mapPagerToAudit(AuditRiciPagerAlertGroup audit, RiciPager pager) {
        if (pager == null) {
            LOGGER.warn("Pager object is null, cannot map to audit.");
            return;
        }
        audit.setPagerTecid(pager.getTecid());
        audit.setPagerPagerId(pager.getPagerId());
        audit.setPagerSerialNumber(pager.getSerialNumber());
    }

  protected abstract Long getTecidFromResult(RiciPersonWithPagerAndAlertGroups result);
}

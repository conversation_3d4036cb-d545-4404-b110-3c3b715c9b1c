package lu.fujitsu.ts.cgdis.portal.core.domain.audit;

import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * Audit domain model for RICI Pager Alert Group operations.
 * This audit tracks changes to the association between persons and alert groups in the pager system.
 */
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class AuditRiciPagerAlertGroup extends Audit {

    @Getter
    @Setter
    private Long personTecId;

    @Getter
    @Setter
    private String personName;

    @Getter
    @Setter
    private String personCgdisRegistrationNumber;

    @Getter
    @Setter
    private Long alertGroupTecid;

    @Getter
    @Setter
    private String alertGroupName;

    @Getter
    @Setter
    private String alertGroupDescription;

    @Getter
    @Setter
    private Long riciRicSchemaTecid;

    @Getter
    @Setter
    private String riciRicSchemaAlias;

    @Getter
    @Setter
    private Long riciRicRangeTecid;

    @Getter
    @Setter
    private String riciRicRangeName;

    @Getter
    @Setter
    private Boolean alertGroupValue;

    @Getter
    @Setter
    private Long pagerTecid;

    @Getter
    @Setter
    private String pagerPagerId;

    @Getter
    @Setter
    private String pagerSerialNumber;

    public AuditRiciPagerAlertGroup() {
        super(AuditType.RICI_PAGER_ALERT_GROUP);
    }
}
